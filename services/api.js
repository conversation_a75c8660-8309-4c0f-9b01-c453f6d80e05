import { API_SCAN_MENU_URL } from "@env";

export async function scanMenu(imageBase64) {
  try {
    const response = await fetch(API_SCAN_MENU_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      body: JSON.stringify({ imageBase64 }),
    });

    const responseText = await response.text();

    // Verificar si es HTML (página de error)
    if (
      responseText.startsWith("<!DOCTYPE") ||
      responseText.startsWith("<html")
    ) {
      console.error("❌ Received HTML instead of JSON");
      throw new Error("Function returned HTML - check deployment and logs");
    }

    let data;
    try {
      data = JSON.parse(responseText);
    } catch (parseError) {
      console.error("❌ JSON parse error:", parseError);
      console.error("Raw response was:", responseText);
      throw new Error("Invalid JSON response from server");
    }

    if (!response.ok) {
      console.error("❌ API Error:", data);
      throw new Error(data.error || `HTTP ${response.status}`);
    }

    console.log("✅ Success:", data);
    return data;
  } catch (error) {
    console.error("❌ Error en scanMenu:", error);
    throw error;
  }
}
