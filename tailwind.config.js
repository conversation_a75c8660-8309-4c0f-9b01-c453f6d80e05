/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./App.{js,jsx,ts,tsx}",
    "./app/**/*.{js,jsx,ts,tsx}",
    "./components/**/*.{js,jsx,ts,tsx}",
    "./components/*.{js,jsx,ts,tsx}",
    "./*.{js,jsx,ts,tsx}",
  ],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      fontFamily: {
        nunitoBold: ["nunitoBold", "open-sans"],
        nunitoExtraBold: ["nunitoExtraBold", "sans-serif"],
        interRegular: ["interRegular", "open-sans"],
        interSemiBold: ["interSemiBold", "open-sans"],
        interBold: ["interBold", "open-sans"],
      },
      colors: {
        primary: "#A3E4D7",
        secondary: "#81C4B7",
        accent: "#FF8A80",
        neutralDark: "#424242",
        neutralLight: "#F5F5F5",
        midnight: "#1a1a1a",
        sand: "#f0ede5",
        textSecondary: "#B8B8B8",
        carbBlue: "#4FC3F7",
      },
    },
  },
  plugins: [],
};
