import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  TextInput,
  ScrollView,
  Dimensions,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
} from "react-native";

import Screen from "./screen";
import Logo from "./logo";

const { width } = Dimensions.get("window");

const objectives = [
  {
    id: "weight_loss",
    title: "Perder peso",
    subtitle: "Logra tu peso ideal de forma saludable",
    icon: "🎯",
  },
  {
    id: "muscle_gain",
    title: "Ganar músculo",
    subtitle: "Desarrolla masa muscular efectivamente",
    icon: "💪",
  },
  {
    id: "energy",
    title: "Más energía",
    subtitle: "Mejora tu vitalidad diaria",
    icon: "⚡",
  },
];

const restrictions = [
  { id: "vegan", title: "Vegano", icon: "🌱" },
  { id: "keto", title: "Keto", icon: "🥑" },
  { id: "allergies", title: "Alergias", icon: "⚠️" },
  { id: "gluten_free", title: "Sin gluten", icon: "🌾" },
  { id: "lactose_free", title: "Sin lactosa", icon: "🥛" },
];

export default function Onboarding({ onComplete }) {
  const [showWelcome, setShowWelcome] = useState(true);
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedObjective, setSelectedObjective] = useState(null);
  const [selectedRestrictions, setSelectedRestrictions] = useState([]);
  const [height, setHeight] = useState("");
  const [weight, setWeight] = useState("");

  const totalSteps = 3;

  const toggleRestriction = (restriction) => {
    setSelectedRestrictions((prev) =>
      prev.includes(restriction)
        ? prev.filter((r) => r !== restriction)
        : [...prev, restriction]
    );
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStartOnboarding = () => {
    setShowWelcome(false);
  };

  const handleComplete = () => {
    const data = {
      objective: selectedObjective,
      restrictions: selectedRestrictions,
      height: height || null,
      weight: weight || null,
    };
    onComplete(data);
  };

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return selectedObjective !== null;
      case 2:
        return true; // Las restricciones son opcionales
      case 3:
        return true; // La información personal es opcional
      default:
        return false;
    }
  };

  const ProgressBar = () => (
    <View className="flex-row items-center justify-center mb-8">
      {[1, 2, 3].map((step) => (
        <React.Fragment key={step}>
          <View
            className={`w-10 h-10 rounded-full items-center justify-center ${
              step <= currentStep ? "bg-primary" : "bg-gray-200"
            }`}
          >
            <Text
              className={`font-interSemiBold text-sm ${
                step <= currentStep ? "text-white" : "text-gray-400"
              }`}
            >
              {step}
            </Text>
          </View>
          {step < 3 && (
            <View
              className={`w-12 h-1 mx-2 ${
                step < currentStep ? "bg-primary" : "bg-gray-200"
              }`}
            />
          )}
        </React.Fragment>
      ))}
    </View>
  );

  const WelcomeStep = () => (
    <View className="flex-1">
      <View className="flex-1 items-center justify-center px-6">
        <View className="items-center mb-12">
          <View className="w-32 h-32 bg-primary/10 rounded-full items-center justify-center mb-8">
            <Text className="text-6xl">🍽️</Text>
          </View>

          <Text className="text-neutralDark text-4xl font-interBold text-center mb-4">
            ¡Bienvenido a Menli!
          </Text>

          <Text className="text-gray-600 text-lg font-interRegular text-center leading-relaxed mb-8">
            Tu asistente personal de nutrición que te ayudará a crear menús
            personalizados y alcanzar tus objetivos de salud
          </Text>
        </View>

        <View className="w-full space-y-4">
          <View className="flex-row items-center bg-white rounded-2xl p-4 shadow-sm">
            <View className="w-12 h-12 bg-green-100 rounded-full items-center justify-center mr-4">
              <Text className="text-2xl">🎯</Text>
            </View>
            <View className="flex-1">
              <Text className="text-neutralDark font-interSemiBold text-base mb-1">
                Objetivos personalizados
              </Text>
              <Text className="text-gray-600 font-interRegular text-sm">
                Menús adaptados a tus metas específicas
              </Text>
            </View>
          </View>

          <View className="flex-row items-center bg-white rounded-2xl p-4 shadow-sm">
            <View className="w-12 h-12 bg-blue-100 rounded-full items-center justify-center mr-4">
              <Text className="text-2xl">🥗</Text>
            </View>
            <View className="flex-1">
              <Text className="text-neutralDark font-interSemiBold text-base mb-1">
                Restricciones alimentarias
              </Text>
              <Text className="text-gray-600 font-interRegular text-sm">
                Respetamos tus preferencias y necesidades
              </Text>
            </View>
          </View>

          <View className="flex-row items-center bg-white rounded-2xl p-4 shadow-sm">
            <View className="w-12 h-12 bg-purple-100 rounded-full items-center justify-center mr-4">
              <Text className="text-2xl">📊</Text>
            </View>
            <View className="flex-1">
              <Text className="text-neutralDark font-interSemiBold text-base mb-1">
                Seguimiento inteligente
              </Text>
              <Text className="text-gray-600 font-interRegular text-sm">
                Monitorea tu progreso de forma sencilla
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* Welcome Screen Button */}
      <View className="px-6 pb-8">
        <TouchableOpacity
          onPress={handleStartOnboarding}
          className="bg-primary rounded-full py-4 px-6"
          activeOpacity={0.8}
        >
          <Text className="text-white text-center font-interSemiBold text-lg">
            Comenzar configuración
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const StepOne = () => (
    <View className="flex-1">
      <Text className="text-neutralDark text-3xl font-interBold text-center mb-4">
        ¿Cuál es tu objetivo?
      </Text>
      <Text className="text-gray-600 text-base font-interRegular text-center mb-8 px-4">
        Selecciona tu meta principal para personalizar tu experiencia
      </Text>

      <View className="space-y-4">
        {objectives.map((obj) => (
          <TouchableOpacity
            key={obj.id}
            activeOpacity={0.8}
            className={`border-2 rounded-2xl p-6 mx-4 ${
              selectedObjective === obj.id
                ? "border-primary bg-primary/5"
                : "border-gray-200 bg-white"
            }`}
            onPress={() => setSelectedObjective(obj.id)}
            style={{
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 3,
            }}
          >
            <View className="flex-row items-center">
              <View className="w-12 h-12 bg-primary/10 rounded-full items-center justify-center mr-4">
                <Text className="text-2xl">{obj.icon}</Text>
              </View>
              <View className="flex-1">
                <Text
                  className={`text-lg font-interSemiBold mb-1 ${
                    selectedObjective === obj.id
                      ? "text-primary"
                      : "text-neutralDark"
                  }`}
                >
                  {obj.title}
                </Text>
                <Text className="text-gray-600 font-interRegular text-sm">
                  {obj.subtitle}
                </Text>
              </View>
              {selectedObjective === obj.id && (
                <View className="w-6 h-6 bg-primary rounded-full items-center justify-center">
                  <Text className="text-white text-xs font-bold">✓</Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const StepTwo = () => (
    <View className="flex-1">
      <Text className="text-neutralDark text-3xl font-interBold text-center mb-4">
        Restricciones alimentarias
      </Text>
      <Text className="text-gray-600 text-base font-interRegular text-center mb-8 px-4">
        Selecciona las restricciones que apliquen para ti (opcional)
      </Text>

      <View className="px-4">
        <View className="flex-row flex-wrap justify-between">
          {restrictions.map((res) => (
            <TouchableOpacity
              key={res.id}
              className={`w-[48%] border-2 rounded-xl p-4 mb-4 items-center ${
                selectedRestrictions.includes(res.id)
                  ? "border-primary bg-primary/5"
                  : "border-gray-200 bg-white"
              }`}
              onPress={() => toggleRestriction(res.id)}
              style={{
                shadowColor: "#000",
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.05,
                shadowRadius: 4,
                elevation: 2,
              }}
            >
              <Text className="text-3xl mb-2">{res.icon}</Text>
              <Text
                className={`font-interSemiBold text-sm text-center ${
                  selectedRestrictions.includes(res.id)
                    ? "text-primary"
                    : "text-neutralDark"
                }`}
              >
                {res.title}
              </Text>
              {selectedRestrictions.includes(res.id) && (
                <View className="absolute top-2 right-2 w-5 h-5 bg-primary rounded-full items-center justify-center">
                  <Text className="text-white text-xs font-bold">✓</Text>
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );

  const StepThree = () => (
    <View className="flex-1">
      <Text className="text-neutralDark text-3xl font-interBold text-center mb-4">
        Información personal
      </Text>
      <Text className="text-gray-600 text-base font-interRegular text-center mb-8 px-4">
        Ayúdanos a personalizar mejor tu experiencia (opcional)
      </Text>

      <View className="px-4">
        <View
          className="bg-white rounded-2xl p-6 mb-6"
          style={{
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 3,
          }}
        >
          <View className="flex-row space-x-4">
            <View className="flex-1">
              <View className="flex-row items-center mb-3">
                <View className="w-8 h-8 bg-primary/10 rounded-full items-center justify-center mr-2">
                  <Text className="text-primary text-sm">📏</Text>
                </View>
                <Text className="text-neutralDark font-interSemiBold">
                  Altura
                </Text>
              </View>
              <View className="flex-row items-center bg-gray-50 rounded-xl px-4 py-3">
                <TextInput
                  className="flex-1 font-interRegular"
                  placeholder="170"
                  value={height}
                  onChangeText={(text) => setHeight(text)}
                  keyboardType="decimal-pad"
                  placeholderTextColor="#9CA3AF"
                  returnKeyType="done"
                  maxLength={3}
                  blurOnSubmit={false}
                  autoCorrect={false}
                  autoCapitalize="none"
                  selectTextOnFocus={false}
                />
                <Text className="text-gray-500 font-interRegular ml-2">cm</Text>
              </View>
            </View>

            <View className="flex-1">
              <View className="flex-row items-center mb-3">
                <View className="w-8 h-8 bg-primary/10 rounded-full items-center justify-center mr-2">
                  <Text className="text-primary text-sm">⚖️</Text>
                </View>
                <Text className="text-neutralDark font-interSemiBold">
                  Peso
                </Text>
              </View>
              <View className="flex-row items-center bg-gray-50 rounded-xl px-4 py-3">
                <TextInput
                  className="flex-1 font-interRegular"
                  placeholder="70"
                  value={weight}
                  onChangeText={(text) => setWeight(text)}
                  keyboardType="numeric"
                  placeholderTextColor="#9CA3AF"
                  returnKeyType="done"
                  maxLength={3}
                  blurOnSubmit={false}
                  autoCorrect={false}
                  autoCapitalize="none"
                  selectTextOnFocus={false}
                />
                <Text className="text-gray-500 font-interRegular ml-2">kg</Text>
              </View>
            </View>
          </View>
        </View>

        <View className="bg-blue-50 border border-blue-200 rounded-xl p-4">
          <View className="flex-row items-center">
            <Text className="text-blue-600 text-lg mr-2">💡</Text>
            <Text className="text-blue-800 font-interSemiBold text-sm flex-1">
              Esta información nos ayuda a crear recomendaciones más precisas
            </Text>
          </View>
        </View>
      </View>
    </View>
  );

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <StepOne />;
      case 2:
        return <StepTwo />;
      case 3:
        return <StepThree />;
      default:
        return <StepOne />;
    }
  };

  if (showWelcome) {
    return (
      <Screen className="bg-gray-50">
        <StatusBar barStyle="dark-content" backgroundColor="#F9FAFB" />

        <View className="flex-1">
          {/* Header for Welcome */}
          <View className="pt-12 pb-6">
            <View className="items-center">
              <Logo />
            </View>
          </View>

          {/* Welcome Content */}
          <WelcomeStep />
        </View>
      </Screen>
    );
  }

  return (
    <Screen className="bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#F9FAFB" />

      <View className="flex-1">
        {/* Header */}
        <View className="pt-12 pb-6">
          <View className="items-center mb-6">
            {/* <Image
              source={require("../assets/images/menli_mascot.png")}
              className="w-10 h-10"
              resizeMode="contain"
            /> */}
            <Logo />
          </View>

          <ProgressBar />
        </View>

        {/* Content */}
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          className="flex-1"
        >
          <ScrollView
            className="flex-1"
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 100 }}
            keyboardShouldPersistTaps="handled"
          >
            {renderStep()}
          </ScrollView>
        </KeyboardAvoidingView>

        {/* Bottom Navigation */}
        <View className="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-6 py-4">
          <View className="flex-row justify-between items-center">
            {currentStep > 1 ? (
              <TouchableOpacity
                onPress={handleBack}
                className="flex-1 mr-3 bg-gray-100 rounded-full py-4 px-6"
                activeOpacity={0.7}
              >
                <Text className="text-gray-700 text-center font-interSemiBold">
                  Atrás
                </Text>
              </TouchableOpacity>
            ) : (
              <View className="flex-1 mr-3" />
            )}

            <TouchableOpacity
              onPress={handleNext}
              className={`flex-1 ml-3 rounded-full py-4 px-6 ${
                canProceed() ? "bg-primary" : "bg-gray-300"
              }`}
              activeOpacity={0.8}
              disabled={!canProceed()}
            >
              <Text className="text-white text-center font-interSemiBold text-lg">
                {currentStep === totalSteps ? "Comenzar" : "Continuar"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Screen>
  );
}
