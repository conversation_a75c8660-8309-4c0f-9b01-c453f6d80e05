import React from "react";
import { View, Text } from "react-native";

const CalorieTrackerWidget = () => {
  // Calendar data for the week
  const weekDays = [
    { day: "S", date: "9" },
    { day: "M", date: "10" },
    { day: "T", date: "11" },
    { day: "W", date: "12", isActive: true },
    { day: "T", date: "13" },
    { day: "F", date: "14" },
    { day: "S", date: "15" },
  ];

  // Progress calculation (1200 eaten out of 1200 goal = 100%)
  const caloriesEaten = 1200;
  const caloriesGoal = 1200;
  const caloriesBurned = 1200;
  const progress = (caloriesEaten / caloriesGoal) * 100;

  const CircularProgress = ({ progress }) => {
    // Create a simple circular progress using multiple View elements
    // This simulates a circular progress with segments
    const segments = 20;
    const activeSegments = Math.round((progress / 100) * segments);

    return (
      <View className="relative w-36 h-36 items-center justify-center">
        {/* Background circle */}
        <View className="absolute w-36 h-36 rounded-full border-8 border-gray-300" />

        {/* Progress segments */}
        <View className="absolute w-36 h-36">
          {Array.from({ length: segments }, (_, i) => {
            const angle = (i * 360) / segments - 90; // Start from top
            const isActive = i < activeSegments;

            return (
              <View
                key={i}
                className={`absolute w-1 h-4 ${isActive ? "bg-gray-800" : "bg-gray-300"}`}
                style={{
                  transform: [
                    { rotate: `${angle}deg` },
                    { translateY: -64 }, // Half of container height
                  ],
                  transformOrigin: "50% 64px", // Center of rotation
                  left: "50%",
                  top: "50%",
                  marginLeft: -2, // Half of width
                }}
              />
            );
          })}
        </View>

        {/* Center content */}
        <View className="items-center justify-center">
          <Text className="text-3xl font-bold text-gray-800">
            {caloriesGoal}
          </Text>
          <Text className="text-sm font-medium text-gray-600 mt-1">Cals</Text>
        </View>
      </View>
    );
  };

  return (
    <View className="w-80 mx-auto my-8 bg-lime-300 rounded-3xl p-6">
      {/* Calendar Week View */}
      <View className="flex-row justify-between items-center mb-8">
        {weekDays.map((item, index) => (
          <View key={index} className="flex-1 items-center">
            <Text
              className={`text-base font-medium mb-2 ${
                item.isActive ? "text-white" : "text-gray-800"
              }`}
            >
              {item.day}
            </Text>
            <View
              className={`w-9 h-9 rounded-xl items-center justify-center ${
                item.isActive ? "bg-gray-900" : "bg-transparent"
              }`}
            >
              <Text
                className={`text-base font-semibold ${
                  item.isActive ? "text-white" : "text-gray-800"
                }`}
              >
                {item.date}
              </Text>
            </View>
          </View>
        ))}
      </View>

      {/* Calorie Progress Section */}
      <View className="flex-row items-center justify-between">
        {/* Eaten */}
        <View className="flex-1 items-center">
          <View className="mb-2">
            <Text className="text-2xl">🍎</Text>
          </View>
          <Text className="text-xl font-bold text-gray-800 mb-1">
            {caloriesEaten}
          </Text>
          <Text className="text-sm font-medium text-gray-700">Eaten</Text>
        </View>

        {/* Progress Circle */}
        <View className="flex-1 justify-center items-center">
          <CircularProgress progress={progress} />
        </View>

        {/* Burned */}
        <View className="flex-1 items-center">
          <View className="mb-2">
            <Text className="text-2xl">🔥</Text>
          </View>
          <Text className="text-xl font-bold text-gray-800 mb-1">
            {caloriesBurned}
          </Text>
          <Text className="text-sm font-medium text-gray-700">Burned</Text>
        </View>
      </View>
    </View>
  );
};

export default CalorieTrackerWidget;
