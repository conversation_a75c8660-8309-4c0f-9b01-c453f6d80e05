import React, { useState } from "react";
import { View, Text, TouchableOpacity, ActivityIndicator } from "react-native";
import { CameraView as ExpoCameraView } from "expo-camera";
import * as FileSystem from "expo-file-system";
import { X } from "lucide-react-native";
import { scanMenu } from "../services/api";

const CameraView = ({
  cameraRef,
  cameraReady,
  onPhotoTaken,
  onClose,
  onCameraReady,
}) => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);

  const handleTakePicture = async () => {
    // Prevenir múltiples llamadas
    if (loading || !cameraReady) {
      console.log('⚠️ Ignoring photo - already processing or camera not ready');
      return;
    }

    try {
      console.log('📸 Taking picture...');
      
      if (!cameraRef?.current) {
        console.error('❌ Camera ref not available');
        return;
      }

      setLoading(true); // ✅ Marcar como procesando ANTES de tomar foto

      const photo = await cameraRef.current.takePictureAsync({ 
        base64: false,
        quality: 0.8 // ✅ Reducir calidad para mejor performance
      });

      console.log('📸 Photo taken:', photo.uri);

      // ✅ Convertir a base64
      const base64 = await FileSystem.readAsStringAsync(photo.uri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      console.log('🔄 Base64 conversion complete');

      // ✅ Llamar a la API
      console.log('🚀 Calling API...');
      const analysis = await scanMenu(base64);
      setResult(analysis);

      console.log('✅ Analysis complete:', analysis.itemsFound, 'items');

      // ✅ Notificar al padre
      onPhotoTaken?.(photo);

    } catch (error) {
      console.error("❌ Failed to take picture:", error);
    } finally {
      setLoading(false); // ✅ Siempre limpiar estado
    }
  };

  return (
    <View className="flex-1 bg-black">
      <ExpoCameraView
        ref={cameraRef}
        style={{ flex: 1 }}
        onCameraReady={onCameraReady}
        facing="back"
      />

      {!cameraReady && (
        <View className="absolute inset-0 bg-black bg-opacity-50 items-center justify-center">
          <ActivityIndicator size="large" color="white" />
          <Text className="text-white text-lg font-interSemiBold mt-4">
            Preparando cámara...
          </Text>
        </View>
      )}

      {/* Botón cerrar */}
      <TouchableOpacity
        activeOpacity={0.7}
        className="absolute top-4 right-4 w-10 h-10 bg-black bg-opacity-50 rounded-full items-center justify-center"
        onPress={onClose}
        disabled={loading} // ✅ Disable while processing
      >
        <X size={24} color="white" />
      </TouchableOpacity>

      {/* Botón de captura */}
      <View className="absolute bottom-4 left-0 right-0 items-center">
        <TouchableOpacity
          activeOpacity={0.8}
          className={`w-20 h-20 rounded-full items-center justify-center ${
            cameraReady && !loading ? "bg-white" : "bg-gray-400"
          }`}
          onPress={handleTakePicture}
          disabled={!cameraReady || loading} // ✅ Solo usar loading
        >
          {loading ? (
            <ActivityIndicator size="large" color="#000" />
          ) : (
            <View
              className={`w-16 h-16 rounded-full ${
                cameraReady && !loading ? "bg-red-500" : "bg-gray-500"
              }`}
            />
          )}
        </TouchableOpacity>

        <Text className="text-white text-center font-interSemiBold mt-4 text-lg">
          {loading
            ? "Analizando menú..."
            : cameraReady
            ? "Toca para escanear menú"
            : "Preparando cámara..."}
        </Text>
      </View>

      {/* Resultado mejorado */}
      {result && result.success && (
        <View className="absolute bottom-32 left-4 right-4 bg-black/80 rounded-xl p-4">
          <Text className="text-white text-lg font-interSemiBold mb-2">
            🎉 ¡Menú Escaneado!
          </Text>
          <Text className="text-green-400 text-base mb-2">
            {result.itemsFound} platos encontrados
          </Text>
          <Text className="text-gray-300 text-xs">
            Procesado con {result.processedWith}
          </Text>
        </View>
      )}

      {result && !result.success && (
        <View className="absolute bottom-32 left-4 right-4 bg-red-900/80 rounded-xl p-4">
          <Text className="text-white text-lg font-interSemiBold mb-2">
            ❌ Error
          </Text>
          <Text className="text-red-300 text-sm">
            {result.error || 'Error desconocido'}
          </Text>
        </View>
      )}
    </View>
  );
};

export default CameraView;