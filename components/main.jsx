import React, { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import { useCamera } from "../hooks/useCamera";
import { useCameraPermission } from "../hooks/useCameraPermissions";
import Home from "./home";
import CameraView from "./camera-view";
import Onboarding from "./onboarding";

const Main = () => {
  const {
    showCamera,
    openCamera,
    closeCamera,
    cameraRef,
    cameraReady,
    isCapturing,
    takePicture,
    onCameraReady,
  } = useCamera();
  const { permission, hasPermission, isLoading, requestPermission } =
    useCameraPermission();
  const [capturedPhoto, setCapturedPhoto] = useState(null);

  // New state for onboarding completion and data
  const [onboardingCompleted, setOnboardingCompleted] = useState(false);
  const [onboardingData, setOnboardingData] = useState(null);

  const handlePhotoTaken = (photo) => {
    console.log("Photo captured:", photo?.uri);
    setCapturedPhoto(photo);
    closeCamera();
  };

  const handleCloseCamera = () => {
    closeCamera();
  };

  const handleOnboardingComplete = (data) => {
    setOnboardingData(data);
    setOnboardingCompleted(true);
    // Here you could also save data to AsyncStorage or context if needed
    console.log("Onboarding completed with data:", data);
  };

  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <Text className="text-neutralDark text-lg font-interSemiBold">
          Loading...
        </Text>
      </View>
    );
  }

  if (!hasPermission) {
    return (
      <View className="flex-1 items-center justify-center bg-white px-6">
        <Text className="text-neutralDark text-xl text-center font-interSemiBold mb-4">
          Camera Permission Required
        </Text>
        <Text className="text-textSecondary text-base text-center mb-8">
          We need access to your camera to scan menus and help you choose the
          best meal.
        </Text>
        <TouchableOpacity
          className="bg-secondary rounded-full px-8 py-4"
          onPress={requestPermission}
        >
          <Text className="text-white text-lg text-center font-interSemiBold">
            Grant Camera Permission
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Show onboarding if not completed
  if (!onboardingCompleted) {
    return <Onboarding onComplete={handleOnboardingComplete} />;
  }

  // Main app flow
  return (
    <>
      {!showCamera ? (
        <Home openCamera={openCamera} />
      ) : (
        <CameraView
          cameraRef={cameraRef}
          cameraReady={cameraReady}
          isCapturing={isCapturing}
          onPhotoTaken={handlePhotoTaken}
          onClose={handleCloseCamera}
          onCameraReady={onCameraReady}
          onTakePicture={takePicture}
        />
      )}
    </>
  );
};

export default Main;
