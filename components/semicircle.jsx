import React from "react";
import Svg, { Path } from "react-native-svg";
import { View, StyleSheet } from "react-native";

const SemiCircle = () => {
  const radius = 100;
  const diameter = radius * 2;

  const pathData = `M 0 ${radius} A ${radius} ${radius} 0 0 1 ${diameter} ${radius} L ${diameter} ${radius} L 0 ${radius} Z`;

  return (
    <View style={styles.container}>
      <Svg height={radius} width={diameter}>
        <Path d={pathData} fill="#424242" />
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});

export default SemiCircle;
