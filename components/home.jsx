import { View, Text, TouchableOpacity, ScrollView } from "react-native";
import React, { useState } from "react";
import Screen from "./screen";
import Logo from "./logo";
import SemiCircle from "./semicircle";
import NutritionCard from "./nutrition-card";

export default function Home({ openCamera }) {
  // Placeholder data for calories and macros
  const caloriesEaten = 1138;
  const caloriesGoal = 2022;
  const caloriesLeft = 884;
  const caloriesBurned = 345;

  const macros = {
    carbs: { current: 154, goal: 204, color: "#007AFF" },
    protein: { current: 31, goal: 82, color: "#FF9500" },
    fat: { current: 43, goal: 54, color: "#FF3B30" },
  };

  // Helper to calculate progress percentage
  const getProgress = (current, goal) => {
    return Math.min((current / goal) * 100, 100);
  };

  const [selected, setSelected] = useState("today");

  const calorieProgress = getProgress(caloriesEaten, caloriesGoal);

  return (
    <Screen className="bg-gray-50">
      <ScrollView className="flex-1">
        {/* Header */}
        <View className="px-6 pt-4">
          <View className="flex-row justify-between items-center w-full">
            <Text className="font-interBold text-2xl">
              <Logo />
            </Text>
            <View className="flex-row items-start">
              <Text className="text-red-500 text-lg font-bold px- py-1.5 bg-white rounded-full">
                🔥 15
              </Text>
            </View>
          </View>
        </View>

        {/* Today / Weekly buttons */}
        <View className="flex flex-row items-center justify-center mb-2 bg-gray-100 rounded-full p-1 mx-10">
          {/* Today */}
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => setSelected("today")}
            className={`flex-1 rounded-lg px-6 py-3 items-center justify-center ${
              selected === "today" ? "bg-black" : "bg-white"
            }`}
          >
            <Text
              className={`text-base font-interSemiBold ${
                selected === "today" ? "text-white" : "text-black"
              }`}
            >
              Today
            </Text>
          </TouchableOpacity>

          {/* Weekly */}
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => setSelected("weekly")}
            className={`flex-1 rounded-lg px-6 py-3 items-center justify-center ${
              selected === "weekly" ? "bg-black" : "bg-white"
            }`}
          >
            <Text
              className={`text-base font-interSemiBold ${
                selected === "weekly" ? "text-white" : "text-black"
              }`}
            >
              Weekly
            </Text>
          </TouchableOpacity>
        </View>

        {/* Weekly Calendar */}
        <View className="bg-white backdrop-blur-md rounded-2xl p-4 mb-2">
          <View className="flex-row justify-between items-center">
            {[
              { day: "S", date: 10, isToday: false },
              { day: "M", date: 11, isToday: false },
              { day: "T", date: 12, isToday: false },
              { day: "W", date: 13, isToday: false },
              { day: "T", date: 14, isToday: true },
              { day: "F", date: 15, isToday: false },
              { day: "S", date: 16, isToday: false },
            ].map((item, index) => (
              <TouchableOpacity
                key={index}
                className={`items-center justify-center w-10 h-16 rounded-xl ${
                  item.isToday
                    ? "bg-black"
                    : index === 5 || index === 6
                      ? "opacity-50"
                      : ""
                }`}
                activeOpacity={0.7}
              >
                <Text
                  className={`text-sm font-medium mb-1 ${
                    item.isToday
                      ? "text-white"
                      : index === 5 || index === 6
                        ? "text-gray-400"
                        : "text-gray-600"
                  }`}
                >
                  {item.day}
                </Text>
                <Text
                  className={`text-lg font-semibold ${
                    item.isToday
                      ? "text-white"
                      : index === 5 || index === 6
                        ? "text-gray-400"
                        : "text-gray-900"
                  }`}
                >
                  {item.date}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Main Calorie Card  */}
        <NutritionCard />
        {/* Water section  */}

        {/* Latest scans */}
        <Text className="text-left text-xl font-semibold ml-4 mt-4">
          Logged Food
        </Text>
      </ScrollView>
    </Screen>
  );
}
