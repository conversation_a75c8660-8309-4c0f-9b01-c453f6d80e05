import React, { useState } from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import * as Progress from "react-native-progress";

export default function NutritionCard({
  caloriesGoal = 2500,
  caloriesConsumed = 382,
  proteinGoal = 150,
  proteinConsumed = 12,
  carbsGoal = 300,
  carbsConsumed = 49,
  fatsGoal = 70,
  fatsConsumed = 18,
  waterGoal = 2, // en litros
}) {
  const [waterConsumed, setWaterConsumed] = useState(0.9);

  // Calcular valores restantes
  const caloriesLeft = caloriesGoal - caloriesConsumed;
  const proteinsLeft = proteinGoal - proteinConsumed;
  const carbsLeft = carbsGoal - carbsConsumed;
  const fatsLeft = fatsGoal - fatsConsumed;

  // Calcular progresos (entre 0 y 1)
  const caloriesProgress = caloriesConsumed / caloriesGoal;
  const proteinsProgress = proteinConsumed / proteinGoal;
  const carbsProgress = carbsConsumed / carbsGoal;
  const fatsProgress = fatsConsumed / fatsGoal;

  // Agua
  const waterProgress = waterConsumed / waterGoal;
  const recommendedNow = 1.4; // podrías calcularlo en base a la hora del día
  const percentage = Math.round((waterConsumed / waterGoal) * 100);

  const handleAddWater = () =>
    setWaterConsumed((prev) => Math.min(prev + 0.2, waterGoal));
  const handleRemoveWater = () =>
    setWaterConsumed((prev) => Math.max(prev - 0.2, 0));

  return (
    <View className="space-y-5">
      {/* Card grande: Calories */}
      <View className="flex flex-row justify-between items-center py-6 px-4 bg-white rounded-lg mx-2">
        <View className="flex flex-col">
          <Text className="text-5xl font-bold text-gray-800">
            {caloriesLeft}
          </Text>
          <Text className="text-sm text-gray-800">
            Calories <Text className="font-bold">left</Text>
          </Text>
        </View>
        <View style={styles.circleContainer}>
          <Progress.Circle
            size={80}
            progress={caloriesProgress}
            thickness={6}
            color={"#22c55e"}
            unfilledColor={"#e5e7eb"}
            borderWidth={0}
          />
          <Text style={[styles.emojiLarge, styles.centeredEmoji]}>🔥</Text>
        </View>
      </View>

      {/* Cards chicas */}
      <View className="flex flex-row justify-between mx-2 mt-2">
        {/* Proteins */}
        <View className="flex-1 bg-white rounded-lg items-center py-4 mx-1">
          <View style={styles.circleContainer}>
            <Progress.Circle
              size={60}
              progress={proteinsProgress}
              thickness={4}
              color={"#f43f5e"}
              unfilledColor={"#e5e7eb"}
              borderWidth={0}
            />
            <Text style={[styles.emojiSmall, styles.centeredEmoji]}>🍗</Text>
          </View>
          <Text className="mt-2 text-lg font-bold text-gray-800">
            {proteinsLeft}g
          </Text>
          <Text className="text-sm text-gray-500 -mt-1">Proteins left</Text>
        </View>

        {/* Carbs */}
        <View className="flex-1 bg-white rounded-lg items-center py-4 mx-1">
          <View style={styles.circleContainer}>
            <Progress.Circle
              size={60}
              progress={carbsProgress}
              thickness={4}
              color={"#f97316"}
              unfilledColor={"#e5e7eb"}
              borderWidth={0}
            />
            <Text style={[styles.emojiSmall, styles.centeredEmoji]}>🥖</Text>
          </View>
          <Text className="mt-2 text-lg font-bold text-gray-800">
            {carbsLeft}g
          </Text>
          <Text className="text-sm text-gray-500 -mt-1">Carbs left</Text>
        </View>

        {/* Fats */}
        <View className="flex-1 bg-white rounded-lg items-center py-4 mx-1">
          <View style={styles.circleContainer}>
            <Progress.Circle
              size={60}
              progress={fatsProgress}
              thickness={4}
              color={"#22c55e"}
              unfilledColor={"#e5e7eb"}
              borderWidth={0}
            />
            <Text style={[styles.emojiSmall, styles.centeredEmoji]}>🥑</Text>
          </View>
          <Text className="mt-2 text-lg font-bold text-gray-800">
            {fatsLeft}g
          </Text>
          <Text className="text-sm text-gray-500 -mt-1">Fats left</Text>
        </View>
      </View>

      {/* Tracker de agua */}
      <View className="bg-white rounded-lg py-4 px-4 mx-2 mt-3">
        <View className="flex flex-row justify-between items-center">
          <View>
            <Text className="text-blue-600 font-bold text-lg">
              Water {waterConsumed.toFixed(1)}L ({percentage}%)
            </Text>
            <Text className="text-gray-500 text-sm">
              Recommended until now {recommendedNow}L
            </Text>
          </View>

          {/* Botones + y - */}
          <View className="flex flex-row items-center">
            <TouchableOpacity
              onPress={handleRemoveWater}
              style={styles.circleButton}
            >
              <Text className="text-white text-lg">−</Text>
            </TouchableOpacity>
            <Text className="mx-2 text-gray-600">0.2L</Text>
            <TouchableOpacity
              onPress={handleAddWater}
              style={[styles.circleButton, { backgroundColor: "#22c55e" }]}
            >
              <Text className="text-white text-lg">+</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Gotas de agua */}
        <View className="flex flex-row mt-3">
          {Array.from({ length: Math.round(waterGoal / 0.2) }).map((_, i) => {
            const filled = i < Math.round(waterConsumed / 0.2);
            return (
              <Text
                key={i}
                style={{
                  fontSize: 22,
                  marginRight: 4,
                  color: filled ? "#3b82f6" : "#e5e7eb",
                }}
              >
                💧
              </Text>
            );
          })}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  circleContainer: {
    position: "relative",
    justifyContent: "center",
    alignItems: "center",
    padding: "2px",
  },
  centeredEmoji: {
    position: "absolute",
    textAlign: "center",
  },
  emojiLarge: {
    fontSize: 30,
  },
  emojiSmall: {
    fontSize: 20,
  },
  circleButton: {
    width: 34,
    height: 34,
    borderRadius: 17,
    backgroundColor: "#ef4444",
    justifyContent: "center",
    alignItems: "center",
  },
});
