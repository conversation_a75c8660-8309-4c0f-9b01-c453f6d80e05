import { useState, useEffect } from "react";
import { useCameraPermissions } from "expo-camera";

export const useCameraPermission = () => {
  const [permission, requestPermission] = useCameraPermissions();
  const [isRequesting, setIsRequesting] = useState(false);

  const requestCameraPermission = async () => {
    if (isRequesting) return permission;

    try {
      setIsRequesting(true);
      const result = await requestPermission();
      return result;
    } catch (error) {
      console.error("Error requesting camera permission:", error);
      throw error;
    } finally {
      setIsRequesting(false);
    }
  };

  const hasPermission = permission?.granted || false;

  const isLoading = !permission || isRequesting;

  const getPermissionStatus = () => {
    return {
      granted: hasPermission,
      canAskAgain: permission?.canAskAgain ?? true,
      isLoading,
      isRequesting,
      status: permission?.status || "undetermined",
    };
  };

  return {
    // Permission state
    permission,
    hasPermission,
    isLoading,
    isRequesting,

    // Actions
    requestPermission: requestCameraPermission,

    // Utilities
    getPermissionStatus,
  };
};
