import { useRef, useState, useCallback } from "react";
import { CameraView } from "expo-camera";

/**
 * Custom hook for managing camera functionality
 * Provides camera state, operations, and utilities
 */
export const useCamera = () => {
  const [showCamera, setShowCamera] = useState(false);
  const [cameraReady, setCameraReady] = useState(false);
  const [isCapturing, setIsCapturing] = useState(false);
  const cameraRef = useRef(null);

  const openCamera = useCallback(() => {
    setShowCamera(true);
    setCameraReady(false);
    setIsCapturing(false);
  }, []);

  const closeCamera = useCallback(() => {
    setShowCamera(false);
    setCameraReady(false);
    setIsCapturing(false);
  }, []);

  const onCameraReady = useCallback(() => {
    setCameraReady(true);
  }, []);

  /**
   * Takes a picture with the camera
   * @param {Object} options - Picture options
   * @returns {Promise<Object>} Photo data
   */
  const takePicture = useCallback(
    async (options = {}) => {
      if (!cameraReady || !cameraRef.current || isCapturing) {
        throw new Error("Camera not ready or already capturing");
      }

      try {
        setIsCapturing(true);

        const photo = await cameraRef.current.takePictureAsync({
          quality: 0.7,
          skipProcessing: false,
          ...options,
        });

        return photo;
      } catch (error) {
        console.error("Error taking picture:", error);
        throw error;
      } finally {
        setIsCapturing(false);
      }
    },
    [cameraReady, isCapturing]
  );

  /**
   * Gets camera capabilities and status
   */
  const getCameraInfo = useCallback(() => {
    return {
      isReady: cameraReady,
      isVisible: showCamera,
      isCapturing,
      hasPermission: true, // This should come from permissions hook
    };
  }, [cameraReady, showCamera, isCapturing]);

  return {
    // State
    showCamera,
    cameraReady,
    isCapturing,
    cameraRef,

    // Actions
    openCamera,
    closeCamera,
    takePicture,
    onCameraReady,

    // Utilities
    getCameraInfo,
  };
};
