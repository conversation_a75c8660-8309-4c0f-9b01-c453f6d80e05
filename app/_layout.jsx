import { Slot } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { useFonts } from "expo-font";
import "../global.css";

const Layout = () => {
  const [fontsLoaded] = useFonts({
    nunitoBold: require("../assets/fonts/nunito/Nunito-Bold.ttf"),
    nunitoExtraBold: require("../assets/fonts/nunito/Nunito-ExtraBold.ttf"),
    interRegular: require("../assets/fonts/inter/Inter-Regular.ttf"),
    interSemiBold: require("../assets/fonts/inter/Inter-SemiBold.ttf"),
    interBold: require("../assets/fonts/inter/Inter-Bold.ttf"),
  });

  if (!fontsLoaded) return null;

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <Slot />
    </SafeAreaView>
  );
};
export default Layout;
