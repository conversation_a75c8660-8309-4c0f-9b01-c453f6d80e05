{"name": "menli-app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios"}, "dependencies": {"expo": "^54.0.2", "expo-camera": "~17.0.7", "expo-constants": "~18.0.8", "expo-file-system": "~19.0.12", "expo-font": "~14.0.8", "expo-linking": "~8.0.8", "expo-router": "~6.0.1", "expo-status-bar": "~3.0.8", "lucide-react-native": "^0.542.0", "nativewind": "^4.1.23", "react": "19.1.0", "react-native": "0.81.4", "react-native-dotenv": "^3.4.11", "react-native-progress": "^5.0.1", "react-native-reanimated": "~4.1.0", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-svg": "15.12.1", "babel-preset-expo": "~54.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.17"}, "private": true}