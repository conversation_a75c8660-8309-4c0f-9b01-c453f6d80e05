{"expo": {"name": "menli-app", "slug": "menli-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/menli_mascot.png", "userInterfaceStyle": "light", "scheme": "menli://", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.franciscoagustinr.menli-app", "infoPlist": {"NSCameraUsageDescription": "Need camera access 📷"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": [["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera to scan menus."}], "expo-font", "expo-router"]}}